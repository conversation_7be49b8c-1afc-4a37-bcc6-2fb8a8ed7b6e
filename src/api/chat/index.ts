import request from '@/service/request'

// 聊天消息类型
export interface ChatMessage {
  id: string
  type: 'user' | 'assistant' | 'system'
  content: string
  timestamp: number
  status?: 'sending' | 'sent' | 'error'
}

// 发送消息请求参数
export interface SendMessageRequest {
  message: string
  sessionId?: string
  context?: any
}

// AI回复响应
export interface ChatResponse {
  message: string
  sessionId: string
  messageId: string
  timestamp: number
}

// 发送消息到AI助手
export function sendChatMessage(data: SendMessageRequest) {
  return request.post<ChatResponse>({
    url: '/ai-chat/send',
    data,
  })
}

// 获取聊天历史
export function getChatHistory(sessionId: string) {
  return request.get<ChatMessage[]>({
    url: `/ai-chat/history/${sessionId}`,
  })
}

// 创建新的聊天会话
export function createChatSession() {
  return request.post<{ sessionId: string }>({
    url: '/ai-chat/session',
    data: {},
  })
}

// 清空聊天历史
export function clearChatHistory(sessionId: string) {
  return request.delete({
    url: `/ai-chat/history/${sessionId}`,
  })
}

// 质检标准查询
export function queryQualityStandard(query: string) {
  return request.post({
    url: '/ai-chat/quality-standard',
    data: { query },
  })
}
